import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';

import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/cart/domain/entities/cart_item_entity.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/navigation/presentation/navigation_screen.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';

void showProductDetail(BuildContext context, ProductEntity item) {
  final cartBloc = context.read<CartBloc>();
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    showDragHandle: true,
    elevation: 0,
    useRootNavigator: true,
    backgroundColor: AppColors.white,
    builder: (modalContext) {
      return BlocProvider.value(
        value: cartBloc,
        child: ProductDetailContent(item: item),
      );
    },
  );
}

class ProductDetailContent extends StatefulWidget {
  final ProductEntity item;

  const ProductDetailContent({super.key, required this.item});

  @override
  State<ProductDetailContent> createState() => _ProductDetailContentState();
}

class _ProductDetailContentState extends State<ProductDetailContent> {

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, cartState) {
        // Check if product is already in cart
        final cartItem = cartState.cartItems.firstWhere(
              (item) => item.product.id == widget.item.id,
          orElse: () => CartItemEntity(product: widget.item, quantity: 0),
        );
        final currentCartQuantity = cartItem.quantity;

        return BlocBuilder<HomeBloc, HomeState>(
            builder: (context, state) {
              return Container(
                padding: const EdgeInsets.all(20),
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: CachedNetworkImage(
                        imageUrl: widget.item.image,
                        height: 254,
                        width: 254,
                        fit: BoxFit.cover,
                      ),
                    ),
                    const Gap(20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          context.locale.languageCode == "uz"
                              ? widget.item.title_uz
                              : widget.item.title_ru,
                          style: Theme.of(context).textTheme.displayLarge,
                        ),
                        const Gap(20),
                        Text(
                          LocaleKeys.price.tr(
                            namedArgs: {
                              "price":
                              widget.item.price.toDouble().formatAsSpaceSeparated(),
                            },
                          ),
                          style: Theme.of(
                            context,
                          ).textTheme.displaySmall!.copyWith(color: AppColors.red),
                        ),
                      ],
                    ),
                    const Gap(22),

                    BlocBuilder<OrderBloc, OrderState>(
                      builder: (context, state) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  context.read<OrderBloc>().add(
                                    SelectProductDeliveryTypeEvent(
                                      ProductDeliveryType.in_store,
                                    ),
                                  );
                                },
                                child: Container(
                                  height: 50,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color:
                                    state.productDeliveryType ==
                                        ProductDeliveryType.in_store
                                        ? AppColors.green
                                        : AppColors.mediumGrey,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        AppAssets.store,
                                        width: 30,
                                        color:
                                        state.productDeliveryType ==
                                            ProductDeliveryType.in_store
                                            ? AppColors.white
                                            : AppColors.black,
                                      ),
                                      Gap(4),
                                      Text(
                                        "Olib ketish",
                                        style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w600,
                                          color:
                                          state.productDeliveryType ==
                                              ProductDeliveryType.in_store
                                              ? AppColors.white
                                              : AppColors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Gap(10),
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  context.read<OrderBloc>().add(
                                    SelectProductDeliveryTypeEvent(
                                      ProductDeliveryType.delivery,
                                    ),
                                  );
                                },
                                child: Container(
                                  height: 50,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color:
                                    state.productDeliveryType ==
                                        ProductDeliveryType.delivery
                                        ? AppColors.green
                                        : AppColors.mediumGrey,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        AppAssets.delivery,
                                        width: 30,
                                        color:
                                        state.productDeliveryType ==
                                            ProductDeliveryType.delivery
                                            ? AppColors.white
                                            : AppColors.black,
                                      ),
                                      Gap(4),
                                      Text(
                                        "Yetkazib berish",
                                        style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w600,
                                          color:
                                          state.productDeliveryType ==
                                              ProductDeliveryType.delivery
                                              ? AppColors.white
                                              : AppColors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    const Gap(10),
                    if (widget.item.sizes != null) ...[
                      Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate((widget.item.sizes as List).length, (
                            index,
                            ) {
                          final size = widget.item.sizes[index];
                          return Expanded(
                            child: GestureDetector(
                              onTap: () {
                                context.read<OrderBloc>().add(
                                  SelectSizeOfProductEvent(size),
                                );
                              },
                              child: BlocBuilder<OrderBloc, OrderState>(
                                builder: (context, state) {
                                  return Container(
                                    height: 50,
                                    margin: EdgeInsets.symmetric(
                                      horizontal: index == 0 ? 0 : 5,
                                    ),
                                    padding: const EdgeInsets.symmetric(horizontal: 10),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color:
                                      state.selectedSizeOfProduct == size
                                          ? AppColors.green
                                          : AppColors.mediumGrey,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      size,
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color:
                                        state.selectedSizeOfProduct == size
                                            ? AppColors.white
                                            : AppColors.black,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          );
                        }),
                      ),
                    ],
                    const Gap(30),
                    Text(
                      LocaleKeys.deliveryAddress.tr(),
                      style: TextStyle(
                        color: AppColors.darkGrey,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                    const Gap(30),
                    Column(
                      children: [
                        // Price display
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              LocaleKeys.commonPrice.tr(),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.darkGrey,
                              ),
                            ),
                            Text(
                              LocaleKeys.price.tr(
                                namedArgs: {
                                  "price": currentCartQuantity > 0
                                      ? (widget.item.price * currentCartQuantity).toDouble().formatAsSpaceSeparated()
                                      : widget.item.price.toDouble().formatAsSpaceSeparated(),
                                },
                              ),
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ],
                        ),
                        const Gap(20),

                        // Buttons section
                        if (currentCartQuantity == 0) ...[
                          // Add to Cart button when no items in cart
                          WButton(
                            txt: 'Savatga qo\'shish',
                            onTap: () {
                              context.read<CartBloc>().add(AddToCart(
                                product: widget.item,
                                quantity: 1,
                              ));
                            },
                          ),
                        ] else ...[
                          // Quantity controls when items are in cart
                          Container(
                            height: 60,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(30),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    if (currentCartQuantity > 1) {
                                      context.read<CartBloc>().add(UpdateQuantity(
                                        productId: widget.item.id,
                                        quantity: currentCartQuantity - 1,
                                      ));
                                    } else {
                                      context.read<CartBloc>().add(RemoveFromCart(
                                        productId: widget.item.id,
                                      ));
                                    }
                                  },
                                  child: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Icon(
                                      Icons.remove,
                                      color: AppColors.primary,
                                      size: 20,
                                    ),
                                  ),
                                ),
                                Text(
                                  '$currentCartQuantity',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.white,
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    context.read<CartBloc>().add(UpdateQuantity(
                                      productId: widget.item.id,
                                      quantity: currentCartQuantity + 1,
                                    ));
                                  },
                                  child: Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Icon(
                                      Icons.add,
                                      color: AppColors.primary,
                                      size: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Gap(12),
                          // Go to Cart button
                          WButton(
                            txt: 'Savatga o\'tish',
                            btnColor: AppColors.green,
                            onTap: () {
                              Navigator.of(context).pop();
                              // Navigate to cart tab using query parameter
                              context.go('${AppRouter.navigator}?tab=2');
                            },
                          ),
                        ],
                      ],
                    ),
                    Gap(MediaQuery.of(context).viewPadding.bottom),
                  ],
                ),
              );
            });
      },
    );
  }
}
