import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/cart/presentation/widgets/w_cart_tab.dart';
import 'package:echipta/features/cart/presentation/widgets/w_orders_tab.dart';
import 'package:flutter/material.dart';

class CartScreen extends StatelessWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.background,
          elevation: 0,
          title: const Text(
            'Savat',
            style: TextStyle(
              color: AppColors.dark,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: true,
          bottom: const TabBar(
            labelStyle: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
            ),
            labelColor: AppColors.primary,
            unselectedLabelColor: AppColors.grey,
            indicatorColor: AppColors.primary,
            indicatorWeight: 2,
            dividerHeight: 0,
            tabs: [
              Tab(text: "Savat"),
              Tab(text: "Buyurtmalar"),
            ],
          ),
        ),
        body: Padding(
          padding: EdgeInsets.only(bottom: context.padding.bottom),
          child: const TabBarView(
            children: [
              WCartTab(),
              WOrdersTab(),
            ],
          ),
        ),
      ),
    );
  }
}
