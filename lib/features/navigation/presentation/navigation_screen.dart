import 'dart:io';
import 'dart:ui';

import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/navigation/domain/entities/navbar.dart';
import 'package:echipta/features/navigation/presentation/navigator.dart';
import 'package:echipta/features/navigation/presentation/widgets/nav_bar_item.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:vibration/vibration.dart';
import '../../../generated/locale_keys.g.dart';

enum NavItemEnum { main, rating, cart, ticket, chat, profile }

class NavigationScreen extends StatefulWidget {
  final int? initialTab;

  const NavigationScreen({super.key, this.initialTab});

  static Route route() => MaterialPageRoute<void>(builder: (_) => const NavigationScreen());

  @override
  State<NavigationScreen> createState() => _NavigationScreenState();
}

class _NavigationScreenState extends State<NavigationScreen> with TickerProviderStateMixin {
  late PageController _controller;

  final Map<NavItemEnum, GlobalKey<NavigatorState>> _navigatorKeys = {
    NavItemEnum.main: GlobalKey<NavigatorState>(),
    NavItemEnum.rating: GlobalKey<NavigatorState>(),
    NavItemEnum.cart: GlobalKey<NavigatorState>(),
    NavItemEnum.ticket: GlobalKey<NavigatorState>(),
    NavItemEnum.chat: GlobalKey<NavigatorState>(),
    NavItemEnum.profile: GlobalKey<NavigatorState>(),
  };

  final List<NavBar> lables = [
    const NavBar(title: LocaleKeys.main, id: 0, defIcon: AppAssets.home, selectedIcon: AppAssets.home),
    const NavBar(title: LocaleKeys.rating, id: 1, defIcon: AppAssets.rating, selectedIcon: AppAssets.rating),
    const NavBar(title: "Savat", id: 2, defIcon: AppAssets.basket, selectedIcon: AppAssets.basket),
    const NavBar(title: LocaleKeys.currentTicket, id: 3, defIcon: AppAssets.ticket, selectedIcon: AppAssets.ticket),
    const NavBar(title: LocaleKeys.help, id: 4, defIcon: AppAssets.chat, selectedIcon: AppAssets.chat),
    const NavBar(title: LocaleKeys.account, id: 5, defIcon: AppAssets.profile, selectedIcon: AppAssets.profile),
  ];

  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    final initialPage = widget.initialTab ?? 0;
    _currentIndex = initialPage;
    _controller = PageController(initialPage: initialPage);
    _controller.addListener(onTabChange);
  }

  void onTabChange() {
    final _noScreenshot = NoScreenshot.instance;

    if (kDebugMode) {
      print('Page changed to: ${_controller.page}');
    }

    setState(() {
      _currentIndex = _controller.page?.toInt() ?? 0;
      _navigatorKeys[NavItemEnum.values[_currentIndex]]?.currentState?.popUntil((route) => route.isFirst);
    });

    if (_currentIndex == 2) {
      _noScreenshot.screenshotOff();
    } else {
      _noScreenshot.screenshotOn();
    }
  }

  Widget _buildPageNavigator(NavItemEnum tabItem) =>
      TabNavigator(navigatorKey: _navigatorKeys[tabItem]!, tabItem: tabItem);

  void changePage(int index) {
    setState(() => _currentIndex = index);
    _controller.jumpToPage(index);
  }

  bool isBtmSheetOpened = false;

  @override
  Widget build(BuildContext context) => BlocProvider(
    create: (context) => CartBloc(),
    child: HomeTabControllerProvider(
      controller: _controller,
      child: WillPopScope(
      onWillPop: () async {
        final isFirstRouteInCurrentTab =
            !await _navigatorKeys[NavItemEnum.values[_currentIndex]]!.currentState!.maybePop();
        if (isFirstRouteInCurrentTab) {
          if (NavItemEnum.values[_currentIndex] != NavItemEnum.main) {
            changePage(0);
            return false;
          }
        }
        return isFirstRouteInCurrentTab;
      },
      child: Scaffold(
        extendBody: true,
        resizeToAvoidBottomInset: true,
        bottomNavigationBar: Container(
          height: 70 + MediaQuery.of(context).padding.bottom,
          decoration: BoxDecoration(
            color: AppColors.white.withOpacity(0.85),
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
            border: const Border.fromBorderSide(BorderSide(width: 1, color: AppColors.white)),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5, tileMode: TileMode.decal),
              child: Row(
                children: [
                  NavItemWidget(
                    navBar: lables[0],
                    currentIndex: _currentIndex,
                    onTap: () {
                      vibrateOnTab();
                      changePage(0);
                    },
                  ),
                  NavItemWidget(
                    navBar: lables[1],
                    currentIndex: _currentIndex,
                    onTap: () {
                      vibrateOnTab();
                      changePage(1);
                    },
                  ),
                  NavItemWidget(
                    navBar: lables[2],
                    currentIndex: _currentIndex,
                    onTap: () {
                      vibrateOnTab();
                      changePage(2);
                    },
                  ),
                  NavItemWidget(
                    navBar: lables[3],
                    currentIndex: _currentIndex,
                    onTap: () {
                      vibrateOnTab();
                      changePage(3);
                    },
                  ),
                  NavItemWidget(
                    navBar: lables[4],
                    currentIndex: _currentIndex,
                    onTap: () {
                      vibrateOnTab();
                      changePage(4);
                    },
                  ),
                  NavItemWidget(
                    navBar: lables[5],
                    currentIndex: _currentIndex,
                    onTap: () {
                      vibrateOnTab();
                      changePage(5);
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        body: PageView(
          controller: _controller,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            _buildPageNavigator(NavItemEnum.main),
            _buildPageNavigator(NavItemEnum.rating),
            _buildPageNavigator(NavItemEnum.cart),
            _buildPageNavigator(NavItemEnum.ticket),
            _buildPageNavigator(NavItemEnum.chat),
            _buildPageNavigator(NavItemEnum.profile),
          ],
        ),
      ),
    ),
    ),
  );

  void vibrateOnTab() async {
    if (Platform.isIOS) {
      await HapticFeedback.lightImpact();
    } else if (Platform.isAndroid && (await Vibration.hasVibrator() ?? false)) {
      Vibration.vibrate(amplitude: 50, duration: 40, repeat: 1);
      // HapticFeedback.mediumImpact();
    }
  }
}

class HomeTabControllerProvider extends InheritedWidget {
  final PageController controller;

  const HomeTabControllerProvider({required super.child, required this.controller, super.key});

  static HomeTabControllerProvider of(BuildContext context) {
    final result = context.dependOnInheritedWidgetOfExactType<HomeTabControllerProvider>();
    assert(result != null, 'No HomeTabControllerProvider found in context');
    return result!;
  }

  @override
  bool updateShouldNotify(HomeTabControllerProvider oldWidget) => false;
}
